#ifndef CIRCULAR_BUFFER_H
#define CIRCULAR_BUFFER_H

template<typename T, size_t S>
class CircularBuffer {
public:
    CircularBuffer() : head(0), tail(0), full(false) {}

    void push(const T& item) {
        buffer[head] = item;
        if (full) {
            tail = (tail + 1) % S;
        }
        head = (head + 1) % S;
        full = head == tail;
    }

    T pop() {
        if (empty()) {
            return T();
        }
        T val = buffer[tail];
        full = false;
        tail = (tail + 1) % S;
        return val;
    }

    void reset() {
        head = tail = 0;
        full = false;
    }

    bool empty() const {
        return (!full && (head == tail));
    }

    bool isFull() const {
        return full;
    }

    size_t size() const {
        if (full) return S;
        if (head >= tail) return head - tail;
        return S + head - tail;
    }

    // Iterator support for calculating averages
    const T* begin() const {
        return &buffer[0];
    }

    const T* end() const {
        return &buffer[S];
    }

    // Access elements without removing them
    T& operator[](size_t index) {
        return buffer[(tail + index) % S];
    }

    const T& operator[](size_t index) const {
        return buffer[(tail + index) % S];
    }

private:
    T buffer[S];
    size_t head;
    size_t tail;
    bool full;
};

#endif // CIRCULAR_BUFFER_H
