#ifndef BUTTON_HANDLER_H
#define BUTTON_HANDLER_H

#include <Arduino.h>

class ButtonHandler {
public:
    static const unsigned long DEBOUNCE_DELAY = 50;    // 50ms debounce time
    static const unsigned long LONG_PRESS_TIME = 1000; // 1s for long press

    ButtonHandler(uint8_t pin) : 
        _pin(pin),
        _lastDebounceTime(0),
        _lastButtonState(HIGH),
        _buttonState(HIGH),
        _pressStartTime(0),
        _isLongPress(false) {
        pinMode(_pin, INPUT_PULLUP);
    }

    bool wasPressed() {
        return _update() && _buttonState == LOW && !_isLongPress;
    }

    bool wasReleased() {
        return _update() && _buttonState == HIGH;
    }

    bool isLongPress() {
        _update();
        return _isLongPress;
    }

private:
    bool _update() {
        bool stateChanged = false;
        int reading = digitalRead(_pin);
        unsigned long currentTime = millis();

        // If the switch changed, due to noise or pressing
        if (reading != _lastButtonState) {
            _lastDebounceTime = currentTime;
            if (reading == LOW) {
                _pressStartTime = currentTime;
                _isLongPress = false;
            }
        }

        // If the button state has been stable longer than the debounce delay
        if ((currentTime - _lastDebounceTime) > DEBOUNCE_DELAY) {
            // If the button state has changed
            if (reading != _buttonState) {
                _buttonState = reading;
                stateChanged = true;
            }
            
            // Check for long press
            if (_buttonState == LOW && !_isLongPress && 
                (currentTime - _pressStartTime) > LONG_PRESS_TIME) {
                _isLongPress = true;
                stateChanged = true;
            }
        }

        _lastButtonState = reading;
        return stateChanged;
    }

    uint8_t _pin;
    unsigned long _lastDebounceTime;
    int _lastButtonState;
    int _buttonState;
    unsigned long _pressStartTime;
    bool _isLongPress;
};

#endif // BUTTON_HANDLER_H
