#ifndef COLOR_SENSOR_H
#define COLOR_SENSOR_H

#include <Arduino.h>
#include <DFRobot_TCS3430.h>
#include <CircularBuffer.h>

class ColorSensor {
public:
    struct ColorData {
        // Raw sensor data from TCS3430
        uint16_t raw_X, raw_Y, raw_Z;
        
        // Processed CIE 1931 XYZ tristimulus values
        // X: Red-like response
        // Y: Luminance (perceived brightness)
        // Z: Blue-like response
        float X, Y, Z;
        
        // CIE 1931 chromaticity coordinates
        // x = X/(X+Y+Z)
        // y = Y/(X+Y+Z)
        // z = Z/(X+Y+Z) = 1-x-y (derived)
        float x, y;
        float Y_luminance;  // Preserved Y value for luminance
        
        // RGB and Hex representation
        uint8_t r, g, b;
        char hex[8];
        
        // Color matching results
        char closestName[32];
        float deltaE;
        bool isVividWhite;

        bool operator==(const ColorData& other) const {
            return raw_X == other.raw_X && raw_Y == other.raw_Y && raw_Z == other.raw_Z &&
                   X == other.X && Y == other.Y && Z == other.Z &&
                   x == other.x && y == other.y && Y_luminance == other.Y_luminance &&
                   r == other.r && g == other.g && b == other.b &&
                   deltaE == other.deltaE && isVividWhite == other.isVividWhite &&
                   strcmp(hex, other.hex) == 0 &&
                   strcmp(closestName, other.closestName) == 0;
        }
        
        bool operator!=(const ColorData& other) const {
            return !(*this == other);
        }
    };

    static const uint8_t BUFFER_SIZE = 8;  // Size of moving average buffer
    static const uint8_t MIN_INTEGRATION_TIME = 0x20;  // 50ms
    static const uint8_t MAX_INTEGRATION_TIME = 0xFF;  // 650ms
    static const uint8_t DEFAULT_INTEGRATION_TIME = 0x40;  // 100ms

    ColorSensor();
    bool begin();
    bool update();
    const ColorData& getData() const { return currentData; }
    
    // Calibration and configuration
    void calibrate();
    void setGain(uint8_t gain);
    void setIntegrationTime(uint8_t time);
    void enableAdaptiveSampling(bool enable) { adaptiveSamplingEnabled = enable; }

private:
    DFRobot_TCS3430 sensor;
    ColorData currentData;
    CircularBuffer<ColorData, BUFFER_SIZE> dataBuffer;
    bool adaptiveSamplingEnabled;
    uint8_t currentGain;
    uint8_t currentIntegrationTime;
    float calibrationFactor;

    void updateMovingAverage();
    void adjustSettings();
    void calculateChromaticity();
    bool isReadingValid(const ColorData& data);
    void optimizeSettings();
    
    // Thresholds for auto-adjustment
    static const uint16_t SATURATION_THRESHOLD = 65000;  // Near max of 65535
    static const uint16_t LOW_SIGNAL_THRESHOLD = 100;    // Minimum acceptable signal
    static const uint8_t GAIN_LEVELS[4];  // Defined in cpp file
};

#endif // COLOR_SENSOR_H
