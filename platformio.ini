[env:esp32s3]
platform = espressif32
board = um_pros3
framework = arduino
monitor_speed = 115200
upload_speed = 921600
upload_port = COM6
monitor_port = COM6
upload_flags =
    --before=default_reset
    --after=hard_reset
    --chip=esp32s3
    --no-stub
build_flags =
    -DCORE_DEBUG_LEVEL=3
    -DARDUINO_USB_MODE=1
    -DARDUINO_USB_CDC_ON_BOOT=1
    -I${PROJECT_DIR}/src
    -I${PROJECT_DIR}
    -DCONFIG_LITTLEFS_FOR_IDF_3_2
    -DBOARD_HAS_PSRAM
    -DCONFIG_SPIRAM_USE_CAPS_ALLOC=1
    -DCONFIG_SPIRAM_IGNORE_NOTFOUND=1
    -DUM_PROS3_BOARD
build_unflags = -std=gnu++11
build_src_flags = -std=gnu++17
lib_deps =
    bblanchon/Arduino<PERSON><PERSON>@^7.0.0
    WebServer
    LittleFS
    adafruit/Adafruit NeoPixel@^1.10.0
    unexpectedmaker/UMS3 Helper Library@^1.0.0
lib_ldf_mode = deep
board_build.filesystem = littlefs
board_build.flash_size = 16MB
board_build.psram_type = opi
board_build.memory_type = qio_opi
board_build.partitions = huge_app.csv
