#include "PowerManager.h"

PowerManager::PowerManager() :
    currentState(POWER_OFF),
    lastButtonEvent(BUTTON_NONE),
    buttonPressed(false),
    lastButtonState(HIGH),
    buttonPressTime(0),
    buttonReleaseTime(0),
    lastDebounceTime(0),
    clickCount(0),
    longPressDetected(false),
    powerOnTime(0),
    shutdownStartTime(0),
    buzzerActive(false),
    buzzerStartTime(0),
    buzzerDuration(0),
    buzzerBeepCount(0),
    buzzerCurrentBeep(0),
    buzzerBeepDuration(0),
    buzzerBeepInterval(0),
    buzzerLastBeepTime(0)
{
}

bool PowerManager::begin() {
    // Initialize power control pins
    pinMode(SYS_EN_PIN, OUTPUT);
    pinMode(SYS_OUT_PIN, INPUT_PULLUP);
    pinMode(BUZZER_PIN, OUTPUT);
    
    // Set initial states
    setSysEnable(false);  // Start with power disabled
    setBuzzer(false);     // Buzzer off
    
    // Check if power button is pressed to start system
    if (readPowerButton()) {
        powerOn();
        return true;
    }
    
    return false;  // System remains off
}

void PowerManager::update() {
    updateButtonState();
    processButtonEvents();
    updateBuzzer();
    
    // Handle power state transitions
    switch (currentState) {
        case POWER_OFF:
            // Wait for power button press to turn on
            if (readPowerButton() && !buttonPressed) {
                powerOn();
            }
            break;
            
        case POWER_ON:
            // Normal operation - handle button events
            if (lastButtonEvent == BUTTON_LONG_PRESS) {
                // Long press initiates shutdown
                currentState = POWER_SHUTTING_DOWN;
                shutdownStartTime = millis();
                beepPattern(3, 100, 100);  // 3 beeps to indicate shutdown
                clearButtonEvent();
            }
            break;
            
        case POWER_SHUTTING_DOWN:
            // Wait for buzzer pattern to complete, then power off
            if (millis() - shutdownStartTime > POWER_OFF_DELAY_MS && !buzzerActive) {
                powerOff();
            }
            break;
    }
}

void PowerManager::powerOn() {
    setSysEnable(true);
    currentState = POWER_ON;
    powerOnTime = millis();
    beep(200);  // Power on beep
    
    Serial.println("Power Manager: System powered ON");
}

void PowerManager::powerOff() {
    setSysEnable(false);
    currentState = POWER_OFF;
    
    Serial.println("Power Manager: System powered OFF");
}

void PowerManager::emergencyShutdown() {
    beepPattern(5, 50, 50);  // Rapid beeps for emergency
    delay(500);  // Wait for beeps
    powerOff();
}

PowerManager::ButtonEvent PowerManager::getLastButtonEvent() {
    ButtonEvent event = lastButtonEvent;
    lastButtonEvent = BUTTON_NONE;
    return event;
}

void PowerManager::beep(uint16_t duration_ms) {
    buzzerActive = true;
    buzzerStartTime = millis();
    buzzerDuration = duration_ms;
    buzzerBeepCount = 1;
    buzzerCurrentBeep = 0;
    setBuzzer(true);
}

void PowerManager::beepPattern(uint8_t count, uint16_t duration_ms, uint16_t interval_ms) {
    buzzerActive = true;
    buzzerBeepCount = count;
    buzzerCurrentBeep = 0;
    buzzerBeepDuration = duration_ms;
    buzzerBeepInterval = interval_ms;
    buzzerLastBeepTime = millis();
    setBuzzer(true);
}

void PowerManager::updateButtonState() {
    bool currentReading = readPowerButton();
    uint32_t currentTime = millis();
    
    // Debounce logic
    if (currentReading != lastButtonState) {
        lastDebounceTime = currentTime;
    }
    
    if ((currentTime - lastDebounceTime) > BUTTON_DEBOUNCE_MS) {
        if (currentReading != buttonPressed) {
            buttonPressed = currentReading;
            
            if (buttonPressed) {
                // Button press detected
                buttonPressTime = currentTime;
                longPressDetected = false;
            } else {
                // Button release detected
                buttonReleaseTime = currentTime;
            }
        }
    }
    
    lastButtonState = currentReading;
}

void PowerManager::processButtonEvents() {
    uint32_t currentTime = millis();
    
    // Check for long press while button is held
    if (buttonPressed && !longPressDetected && 
        (currentTime - buttonPressTime) > LONG_PRESS_TIME_MS) {
        longPressDetected = true;
        lastButtonEvent = BUTTON_LONG_PRESS;
        return;
    }
    
    // Process button release events
    if (!buttonPressed && buttonReleaseTime > buttonPressTime) {
        uint32_t pressDuration = buttonReleaseTime - buttonPressTime;
        
        // Ignore if it was a long press (already handled)
        if (longPressDetected) {
            longPressDetected = false;
            return;
        }
        
        // Count clicks for double-click detection
        if (pressDuration < LONG_PRESS_TIME_MS) {
            clickCount++;
            
            // Wait for potential second click
            if (clickCount == 1) {
                // Start waiting for double click
                return;
            } else if (clickCount == 2) {
                // Double click detected
                lastButtonEvent = BUTTON_DOUBLE_PRESS;
                clickCount = 0;
                return;
            }
        }
    }
    
    // Handle single click timeout
    if (clickCount == 1 && 
        (currentTime - buttonReleaseTime) > DOUBLE_CLICK_TIME_MS) {
        lastButtonEvent = BUTTON_SINGLE_PRESS;
        clickCount = 0;
    }
}

void PowerManager::updateBuzzer() {
    if (!buzzerActive) return;
    
    uint32_t currentTime = millis();
    
    if (buzzerBeepCount == 1) {
        // Single beep
        if (currentTime - buzzerStartTime >= buzzerDuration) {
            setBuzzer(false);
            buzzerActive = false;
        }
    } else {
        // Beep pattern
        uint32_t timeSinceLastBeep = currentTime - buzzerLastBeepTime;
        
        if (buzzerCurrentBeep < buzzerBeepCount) {
            if (timeSinceLastBeep < buzzerBeepDuration) {
                setBuzzer(true);
            } else if (timeSinceLastBeep < (buzzerBeepDuration + buzzerBeepInterval)) {
                setBuzzer(false);
            } else {
                // Start next beep
                buzzerCurrentBeep++;
                buzzerLastBeepTime = currentTime;
                if (buzzerCurrentBeep < buzzerBeepCount) {
                    setBuzzer(true);
                } else {
                    setBuzzer(false);
                    buzzerActive = false;
                }
            }
        }
    }
}

void PowerManager::setBuzzer(bool state) {
    digitalWrite(BUZZER_PIN, state ? HIGH : LOW);
}

void PowerManager::setSysEnable(bool state) {
    digitalWrite(SYS_EN_PIN, state ? HIGH : LOW);
}

bool PowerManager::readPowerButton() {
    return digitalRead(SYS_OUT_PIN) == LOW;  // Active low
}
