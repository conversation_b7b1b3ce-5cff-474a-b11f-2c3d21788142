/*
 * ESP32-S3 Color Matching Device - Simplified Working Version
 * 
 * A minimal viable version that compiles successfully and provides core functionality.
 * This version focuses on getting the hardware working reliably before adding
 * complex web UI features.
 * 
 * Features:
 * - TCS3430 color sensor reading with I2C communication
 * - Basic WiFi connectivity using ESP32 built-in WiFi
 * - Simple color matching with built-in color database
 * - LED status indicators
 * - Serial output for debugging and data display
 * - Basic error handling and sensor health checks
 * 
 * Hardware Configuration:
 * - I2C: SDA=8, SCL=9
 * - Status LED: GPIO 5
 * - Illumination LED: GPIO 4  
 * - Sensor Interrupt: GPIO 21
 * 
 * Network Configuration:
 * - SSID: "Wifi 6"
 * - Password: "Scrofani1985"
 * - Static IP: *************
 */

#include <Arduino.h>
#include <Wire.h>
#include <WiFi.h>
#include <DFRobot_TCS3430.h>
#include <ArduinoJson.h>
#include <math.h>

// ------------------------------------------------------------------------------------
// Configuration Constants
// ------------------------------------------------------------------------------------
#define I2C_SDA_PIN          8
#define I2C_SCL_PIN          9
#define STATUS_LED_PIN       5
#define ILLUMINATION_LED_PIN 4
#define SENSOR_INTERRUPT_PIN 21
#define READING_INTERVAL_MS  500

// Network Configuration
const char* WIFI_SSID = "Wifi 6";
const char* WIFI_PASSWORD = "Scrofani1985";
const IPAddress STATIC_IP(192, 168, 0, 201);
const IPAddress GATEWAY(192, 168, 0, 1);
const IPAddress SUBNET(255, 255, 255, 0);

// ------------------------------------------------------------------------------------
// Global Variables
// ------------------------------------------------------------------------------------
DFRobot_TCS3430 colorSensor;
unsigned long lastReading = 0;
unsigned long lastStatusBlink = 0;
bool statusLedState = false;
bool illuminationState = false;
bool sensorInitialized = false;
bool wifiConnected = false;

// Color data structure
struct ColorData {
    uint16_t x, y, z;
    uint16_t ir1, ir2;
    uint8_t r, g, b;
    char hexColor[8];
    char matchedColor[32];
    float deltaE;
};

ColorData currentColor = {0};

// Simple color database (expandable later)
struct ReferenceColor {
    const char* name;
    uint8_t r, g, b;
};

const ReferenceColor colorDatabase[] = {
    {"White", 255, 255, 255},
    {"Black", 0, 0, 0},
    {"Red", 255, 0, 0},
    {"Green", 0, 255, 0},
    {"Blue", 0, 0, 255},
    {"Yellow", 255, 255, 0},
    {"Cyan", 0, 255, 255},
    {"Magenta", 255, 0, 255},
    {"Orange", 255, 165, 0},
    {"Purple", 128, 0, 128},
    {"Brown", 165, 42, 42},
    {"Gray", 128, 128, 128},
    {"Pink", 255, 192, 203},
    {"Lime", 0, 255, 0},
    {"Navy", 0, 0, 128}
};

const int COLOR_DATABASE_SIZE = sizeof(colorDatabase) / sizeof(colorDatabase[0]);

// ------------------------------------------------------------------------------------
// Utility Functions
// ------------------------------------------------------------------------------------
void blinkStatusLED() {
    if (millis() - lastStatusBlink > 500) {
        statusLedState = !statusLedState;
        digitalWrite(STATUS_LED_PIN, statusLedState ? HIGH : LOW);
        lastStatusBlink = millis();
    }
}

void setIlluminationLED(bool state) {
    illuminationState = state;
    digitalWrite(ILLUMINATION_LED_PIN, state ? HIGH : LOW);
}

// Simple color distance calculation (Euclidean distance in RGB space)
float calculateColorDistance(uint8_t r1, uint8_t g1, uint8_t b1, uint8_t r2, uint8_t g2, uint8_t b2) {
    float dr = r1 - r2;
    float dg = g1 - g2;
    float db = b1 - b2;
    return sqrt(dr*dr + dg*dg + db*db);
}

// Find closest color match in database
void findColorMatch(uint8_t r, uint8_t g, uint8_t b) {
    float minDistance = 999999.0;
    int bestMatch = 0;
    
    for (int i = 0; i < COLOR_DATABASE_SIZE; i++) {
        float distance = calculateColorDistance(r, g, b, 
                                              colorDatabase[i].r, 
                                              colorDatabase[i].g, 
                                              colorDatabase[i].b);
        if (distance < minDistance) {
            minDistance = distance;
            bestMatch = i;
        }
    }
    
    strcpy(currentColor.matchedColor, colorDatabase[bestMatch].name);
    currentColor.deltaE = minDistance;
}

// Convert RGB to hex string
void rgbToHex(uint8_t r, uint8_t g, uint8_t b, char* hexStr) {
    sprintf(hexStr, "#%02X%02X%02X", r, g, b);
}

// ------------------------------------------------------------------------------------
// Sensor Functions
// ------------------------------------------------------------------------------------
bool initializeSensor() {
    Serial.println("Initializing TCS3430 color sensor...");
    
    if (!colorSensor.begin()) {
        Serial.println("ERROR: Failed to initialize TCS3430 sensor!");
        return false;
    }
    
    // Configure sensor settings
    colorSensor.setALSGain(1);           // Set ALS gain
    colorSensor.setIntegrationTime(0x40); // Set integration time
    
    Serial.println("TCS3430 sensor initialized successfully");
    return true;
}

bool readColorSensor() {
    // Read raw sensor data
    currentColor.x = colorSensor.getXData();
    currentColor.y = colorSensor.getYData();
    currentColor.z = colorSensor.getZData();
    currentColor.ir1 = colorSensor.getIR1Data();
    currentColor.ir2 = colorSensor.getIR2Data();
    
    // Simple XYZ to RGB conversion (basic approximation)
    // This is a simplified conversion - can be improved later
    float xNorm = currentColor.x / 65535.0;
    float yNorm = currentColor.y / 65535.0;
    float zNorm = currentColor.z / 65535.0;
    
    // Basic RGB conversion (simplified)
    currentColor.r = constrain(xNorm * 255, 0, 255);
    currentColor.g = constrain(yNorm * 255, 0, 255);
    currentColor.b = constrain(zNorm * 255, 0, 255);
    
    // Generate hex color string
    rgbToHex(currentColor.r, currentColor.g, currentColor.b, currentColor.hexColor);
    
    // Find closest color match
    findColorMatch(currentColor.r, currentColor.g, currentColor.b);
    
    return true;
}

// ------------------------------------------------------------------------------------
// WiFi Functions
// ------------------------------------------------------------------------------------
void initializeWiFi() {
    Serial.println("Connecting to WiFi...");
    
    WiFi.mode(WIFI_STA);
    WiFi.config(STATIC_IP, GATEWAY, SUBNET);
    WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
    
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
        delay(500);
        Serial.print(".");
        attempts++;
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        wifiConnected = true;
        Serial.println();
        Serial.print("WiFi connected! IP address: ");
        Serial.println(WiFi.localIP());
    } else {
        Serial.println();
        Serial.println("WiFi connection failed - continuing without network");
        wifiConnected = false;
    }
}

// ------------------------------------------------------------------------------------
// Main Functions
// ------------------------------------------------------------------------------------
void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("============================================================");
    Serial.println("ESP32-S3 Color Matching Device - Simplified Version");
    Serial.println("============================================================");
    
    // Initialize GPIO pins
    pinMode(STATUS_LED_PIN, OUTPUT);
    pinMode(ILLUMINATION_LED_PIN, OUTPUT);
    pinMode(SENSOR_INTERRUPT_PIN, INPUT_PULLUP);
    
    digitalWrite(STATUS_LED_PIN, LOW);
    digitalWrite(ILLUMINATION_LED_PIN, LOW);
    
    // Initialize I2C
    Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);
    Wire.setClock(400000); // 400kHz
    Serial.println("I2C initialized");
    
    // Initialize color sensor
    sensorInitialized = initializeSensor();
    if (!sensorInitialized) {
        Serial.println("CRITICAL: Sensor initialization failed!");
        Serial.println("Check I2C connections and sensor power");
    }
    
    // Initialize WiFi
    initializeWiFi();
    
    // Turn on illumination LED
    setIlluminationLED(true);
    
    Serial.println("============================================================");
    Serial.println("System initialization complete!");
    Serial.println("Starting color sensor readings...");
    Serial.println("============================================================");
}

void loop() {
    // Blink status LED to show system is alive
    blinkStatusLED();
    
    // Read color sensor at specified interval
    if (sensorInitialized && millis() - lastReading >= READING_INTERVAL_MS) {
        lastReading = millis();
        
        if (readColorSensor()) {
            // Print color data to serial
            Serial.println("--- Color Reading ---");
            Serial.printf("Raw XYZ: X=%d, Y=%d, Z=%d\n", currentColor.x, currentColor.y, currentColor.z);
            Serial.printf("Raw IR: IR1=%d, IR2=%d\n", currentColor.ir1, currentColor.ir2);
            Serial.printf("RGB: R=%d, G=%d, B=%d\n", currentColor.r, currentColor.g, currentColor.b);
            Serial.printf("Hex: %s\n", currentColor.hexColor);
            Serial.printf("Closest Match: %s (Distance: %.1f)\n", currentColor.matchedColor, currentColor.deltaE);
            Serial.printf("WiFi Status: %s\n", wifiConnected ? "Connected" : "Disconnected");
            Serial.println("--------------------");
        } else {
            Serial.println("ERROR: Failed to read color sensor");
        }
    }
    
    // Check WiFi connection periodically
    if (wifiConnected && WiFi.status() != WL_CONNECTED) {
        Serial.println("WiFi connection lost");
        wifiConnected = false;
    }
    
    // Small delay to prevent overwhelming the system
    delay(10);
}
