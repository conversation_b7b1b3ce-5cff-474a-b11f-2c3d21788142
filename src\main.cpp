/*
 * ESP32-S3 Color Matching Device - Working Version with WiFi
 * 
 * A working version that compiles successfully and provides core functionality
 * with proper WiFi connectivity to "Wifi 6" network.
 * 
 * Features:
 * - TCS3430 color sensor reading with I2C communication
 * - WiFi connectivity using ESP32 built-in WiFi
 * - Simple color matching with built-in color database
 * - LED status indicators
 * - Serial output for debugging and data display
 * - Basic error handling and sensor health checks
 * 
 * Hardware Configuration:
 * - I2C: SDA=8, SCL=9
 * - Status LED: GPIO 5
 * - Illumination LED: GPIO 4  
 * - Sensor Interrupt: GPIO 21
 * 
 * Network Configuration:
 * - SSID: "Wifi 6"
 * - Password: "Scrofani1985"
 * - Static IP: *************
 */

#include <Arduino.h>
#include <Wire.h>
#include <WiFi.h>
#include "DFRobot_TCS3430.h"  // Use local library
#include <ArduinoJson.h>
#include <math.h>
#include <WebServer.h>
#include <LittleFS.h>
#include <Adafruit_NeoPixel.h>

// ------------------------------------------------------------------------------------
// Configuration Constants
// ------------------------------------------------------------------------------------
#define I2C_SDA_PIN          8
#define I2C_SCL_PIN          9
#define ILLUMINATION_LED_PIN 5  // White LED for scanning illumination
#define RGB_LED_PIN          18 // ProS3 onboard RGB LED (WS2812)
#define SENSOR_INTERRUPT_PIN 21
#define READING_INTERVAL_MS  500
#define MAX_COLOR_NAME_LEN   35
#define MAX_DULUX_COLORS     1500

// Network Configuration
const char* WIFI_SSID = "Wifi 6";
const char* WIFI_PASSWORD = "Scrofani1985";
const IPAddress STATIC_IP(192, 168, 0, 201);
const IPAddress GATEWAY(192, 168, 0, 1);
const IPAddress SUBNET(255, 255, 255, 0);

// ------------------------------------------------------------------------------------
// Global Variables
// ------------------------------------------------------------------------------------
DFRobot_TCS3430 colorSensor;
Adafruit_NeoPixel rgbLED(1, RGB_LED_PIN, NEO_RGB + NEO_KHZ800); // ProS3 uses NEO_RGB
unsigned long lastReading = 0;
unsigned long lastRGBUpdate = 0;
int rgbColorWheel = 0;
bool illuminationState = false;
bool sensorInitialized = false;
bool wifiConnected = false;

// Color data structure
struct ColorData {
    uint16_t x, y, z;
    uint16_t ir1, ir2;
    uint8_t r, g, b;
    char hexColor[8];
    char matchedColor[32];
    float deltaE;
};

ColorData currentColor = {0};

// Dulux color database structure
struct ReferenceColor {
    char name[MAX_COLOR_NAME_LEN];
    uint8_t r, g, b;
};

ReferenceColor colorDatabase[MAX_DULUX_COLORS];
int COLOR_DATABASE_SIZE = 0;

// Fallback colors if dulux.json fails to load
const ReferenceColor fallbackColors[] = {
    {"White", 255, 255, 255},
    {"Black", 0, 0, 0},
    {"Red", 255, 0, 0},
    {"Green", 0, 255, 0},
    {"Blue", 0, 0, 255},
    {"Yellow", 255, 255, 0},
    {"Cyan", 0, 255, 255},
    {"Magenta", 255, 0, 255},
    {"Orange", 255, 165, 0},
    {"Purple", 128, 0, 128},
    {"Brown", 165, 42, 42},
    {"Gray", 128, 128, 128},
    {"Pink", 255, 192, 203},
    {"Lime", 0, 255, 0},
    {"Navy", 0, 0, 128}
};

const int FALLBACK_COLOR_COUNT = sizeof(fallbackColors) / sizeof(fallbackColors[0]);

// ------------------------------------------------------------------------------------
// Web Server Variables
// ------------------------------------------------------------------------------------
WebServer server(80);
bool isScanning = false;
unsigned long lastWebUpdate = 0;

// ------------------------------------------------------------------------------------
// Dulux Color Database
// ------------------------------------------------------------------------------------
bool loadDuluxDatabase() {
    if (!LittleFS.exists("/dulux.json")) {
        Serial.println("dulux.json not found, using built-in colors");
        return false;
    }

    File file = LittleFS.open("/dulux.json", "r");
    if (!file) {
        Serial.println("Failed to open dulux.json");
        return false;
    }

    // Check file size and available memory
    size_t fileSize = file.size();
    size_t freeHeap = ESP.getFreeHeap();
    size_t freePsram = ESP.getFreePsram();

    Serial.printf("Dulux JSON file size: %d bytes\n", fileSize);
    Serial.printf("Free heap: %d bytes, Free PSRAM: %d bytes\n", freeHeap, freePsram);

    // If file is too large for available memory, skip loading
    if (fileSize > (freeHeap / 2)) {
        Serial.println("JSON file too large for available heap memory");
        Serial.println("Skipping Dulux database loading to prevent crashes");
        file.close();
        return false;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    Serial.printf("Free heap after JSON parsing: %d bytes\n", ESP.getFreeHeap());

    if (error) {
        Serial.print("Failed to parse dulux.json: ");
        Serial.println(error.c_str());
        Serial.println("This is likely due to the JSON file being too large for available memory");
        Serial.println("Using fallback color database instead");
        return false;
    }

    // Clear existing database
    COLOR_DATABASE_SIZE = 0;

    JsonArray colors = doc.as<JsonArray>();
    for (JsonVariant colorVar : colors) {
        if (COLOR_DATABASE_SIZE >= MAX_DULUX_COLORS) break;

        JsonObject color = colorVar.as<JsonObject>();
        if (!color["name"].is<const char*>() || !color["r"].is<int>() ||
            !color["g"].is<int>() || !color["b"].is<int>()) continue;

        strncpy(colorDatabase[COLOR_DATABASE_SIZE].name, color["name"], MAX_COLOR_NAME_LEN - 1);
        colorDatabase[COLOR_DATABASE_SIZE].name[MAX_COLOR_NAME_LEN - 1] = '\0';
        colorDatabase[COLOR_DATABASE_SIZE].r = color["r"];
        colorDatabase[COLOR_DATABASE_SIZE].g = color["g"];
        colorDatabase[COLOR_DATABASE_SIZE].b = color["b"];
        COLOR_DATABASE_SIZE++;
    }

    Serial.printf("Loaded %d colors from dulux.json\n", COLOR_DATABASE_SIZE);
    return true;
}

// ------------------------------------------------------------------------------------
// Helper Functions
// ------------------------------------------------------------------------------------
// Color wheel function - cycles through rainbow colors
uint32_t colorWheel(byte wheelPos) {
    wheelPos = 255 - wheelPos;
    if (wheelPos < 85) {
        return rgbLED.Color(255 - wheelPos * 3, 0, wheelPos * 3);
    }
    if (wheelPos < 170) {
        wheelPos -= 85;
        return rgbLED.Color(0, wheelPos * 3, 255 - wheelPos * 3);
    }
    wheelPos -= 170;
    return rgbLED.Color(wheelPos * 3, 255 - wheelPos * 3, 0);
}

void updateRGBStatusLED() {
    // Update RGB LED every 15ms for smooth color cycling
    if (millis() - lastRGBUpdate > 15) {
        // Cycle through rainbow colors
        uint32_t color = colorWheel(rgbColorWheel);
        rgbLED.setPixelColor(0, color);
        rgbLED.show();

        // Debug output every 5 seconds
        static unsigned long lastDebug = 0;
        if (millis() - lastDebug > 5000) {
            Serial.printf("ProS3 RGB LED: wheel=%d, color=0x%06X, pin=%d\n",
                         rgbColorWheel, color, RGB_LED_PIN);
            lastDebug = millis();
        }

        rgbColorWheel++;
        if (rgbColorWheel >= 256) {
            rgbColorWheel = 0;
        }
        lastRGBUpdate = millis();
    }
}

void setIlluminationLED(bool state) {
    illuminationState = state;
    digitalWrite(ILLUMINATION_LED_PIN, state ? HIGH : LOW);
}

// Simple color distance calculation (Euclidean distance in RGB space)
float calculateColorDistance(uint8_t r1, uint8_t g1, uint8_t b1, uint8_t r2, uint8_t g2, uint8_t b2) {
    float dr = r1 - r2;
    float dg = g1 - g2;
    float db = b1 - b2;
    return sqrt(dr*dr + dg*dg + db*db);
}

// Find closest color match in database
void findColorMatch(uint8_t r, uint8_t g, uint8_t b) {
    float minDistance = 999999.0;
    int bestMatch = 0;
    
    for (int i = 0; i < COLOR_DATABASE_SIZE; i++) {
        float distance = calculateColorDistance(r, g, b, 
                                              colorDatabase[i].r, 
                                              colorDatabase[i].g, 
                                              colorDatabase[i].b);
        if (distance < minDistance) {
            minDistance = distance;
            bestMatch = i;
        }
    }
    
    strcpy(currentColor.matchedColor, colorDatabase[bestMatch].name);
    currentColor.deltaE = minDistance;
}

// Convert RGB to hex string
void rgbToHex(uint8_t r, uint8_t g, uint8_t b, char* hexStr) {
    sprintf(hexStr, "#%02X%02X%02X", r, g, b);
}

// ------------------------------------------------------------------------------------
// Sensor Functions
// ------------------------------------------------------------------------------------
bool initializeSensor() {
    Serial.println("Initializing TCS3430 color sensor...");
    
    if (!colorSensor.begin()) {
        Serial.println("ERROR: Failed to initialize TCS3430 sensor!");
        return false;
    }
    
    // Configure sensor settings
    colorSensor.setALSGain(1);           // Set ALS gain
    colorSensor.setIntegrationTime(0x40); // Set integration time
    
    Serial.println("TCS3430 sensor initialized successfully");
    return true;
}

bool readColorSensor() {
    if (!sensorInitialized) {
        Serial.println("ERROR: Sensor not initialized");
        return false;
    }

    // Read raw XYZ and IR values
    currentColor.x = colorSensor.getXData();
    currentColor.y = colorSensor.getYData();
    currentColor.z = colorSensor.getZData();
    currentColor.ir1 = colorSensor.getIR1Data();
    currentColor.ir2 = colorSensor.getIR2Data();

    // Debug: Print raw sensor values
    Serial.printf("Raw sensor data: X=%d, Y=%d, Z=%d, IR1=%d, IR2=%d\n",
                  currentColor.x, currentColor.y, currentColor.z, currentColor.ir1, currentColor.ir2);

    // Check for saturation and adjust gain if needed
    if (colorSensor.isSaturated()) {
        Serial.println("WARNING: Sensor saturated, consider reducing gain");
        uint8_t optimalGain = colorSensor.getOptimalGain();
        Serial.printf("Current gain should be: %d\n", optimalGain);
    }

    // Get calibrated XYZ values if calibration is available
    float calibratedX, calibratedY, calibratedZ;
    bool isCalibrated = colorSensor.getCalibratedXYZ(&calibratedX, &calibratedY, &calibratedZ);

    if (isCalibrated) {
        // Use proper XYZ to RGB conversion with calibrated values
        colorSensor.convertXYZtoRGB(calibratedX, calibratedY, calibratedZ,
                                   (uint8_t*)&currentColor.r,
                                   (uint8_t*)&currentColor.g,
                                   (uint8_t*)&currentColor.b);
        Serial.printf("Calibrated XYZ: X=%.3f, Y=%.3f, Z=%.3f -> RGB(%d,%d,%d)\n",
                     calibratedX, calibratedY, calibratedZ, currentColor.r, currentColor.g, currentColor.b);
    } else {
        // Fallback to improved raw conversion
        float X = currentColor.x / 65535.0;
        float Y = currentColor.y / 65535.0;
        float Z = currentColor.z / 65535.0;

        colorSensor.convertXYZtoRGB(X, Y, Z,
                                   (uint8_t*)&currentColor.r,
                                   (uint8_t*)&currentColor.g,
                                   (uint8_t*)&currentColor.b);
        Serial.printf("Raw XYZ: X=%.3f, Y=%.3f, Z=%.3f -> RGB(%d,%d,%d)\n",
                     X, Y, Z, currentColor.r, currentColor.g, currentColor.b);
    }

    // Generate hex color string
    rgbToHex(currentColor.r, currentColor.g, currentColor.b, currentColor.hexColor);

    // Find closest color match
    findColorMatch(currentColor.r, currentColor.g, currentColor.b);

    return true;
}

// ------------------------------------------------------------------------------------
// Web Server Functions
// ------------------------------------------------------------------------------------
void handleRoot() {
    File file = LittleFS.open("/index.html", "r");
    if (file) {
        server.streamFile(file, "text/html");
        file.close();
    } else {
        server.send(404, "text/plain", "index.html not found");
    }
}

void handleStyleCSS() {
    File file = LittleFS.open("/style.css", "r");
    if (file) {
        server.streamFile(file, "text/css");
        file.close();
    } else {
        server.send(404, "text/plain", "style.css not found");
    }
}

void handleFullData() {
    JsonDocument doc;
    doc["data_ready"] = true;
    doc["measured_r"] = currentColor.r;
    doc["measured_g"] = currentColor.g;
    doc["measured_b"] = currentColor.b;
    doc["matched_name"] = currentColor.matchedColor;

    // Find matched color RGB values from database
    int matched_r = 0, matched_g = 0, matched_b = 0;
    for (int i = 0; i < COLOR_DATABASE_SIZE; i++) {
        if (strcmp(colorDatabase[i].name, currentColor.matchedColor) == 0) {
            matched_r = colorDatabase[i].r;
            matched_g = colorDatabase[i].g;
            matched_b = colorDatabase[i].b;
            break;
        }
    }

    doc["matched_r"] = matched_r;
    doc["matched_g"] = matched_g;
    doc["matched_b"] = matched_b;
    doc["delta_e"] = currentColor.deltaE;
    doc["confidence"] = currentColor.deltaE < 5 ? "High" : currentColor.deltaE < 15 ? "Medium" : "Low";
    doc["is_scanning"] = isScanning;
    doc["avg_x"] = (float)currentColor.x;
    doc["avg_y"] = (float)currentColor.y;
    doc["avg_z"] = (float)currentColor.z;
    doc["avg_l"] = 0.0; // LAB conversion would go here
    doc["avg_a"] = 0.0;
    doc["avg_b"] = 0.0;
    doc["avg_ir1"] = (float)currentColor.ir1;
    doc["avg_ir2"] = (float)currentColor.ir2;
    doc["led_state"] = illuminationState;

    // Add calibration data for UI
    doc["calib_gain"] = 16; // Default gain
    doc["calib_ir_comp"] = 0.05; // Default IR compensation
    doc["calib_norm"] = 15000; // Default normalization

    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

void handleStartScan() {
    isScanning = true;
    Serial.println("=== SCAN STARTED ===");
    server.send(200, "text/plain", "Scan started");
}

void handleStopScan() {
    isScanning = false;
    Serial.println("=== SCAN STOPPED ===");
    server.send(200, "text/plain", "Scan stopped");
}

void handleToggleLED() {
    // Only allow manual LED toggle when not scanning
    if (!isScanning) {
        illuminationState = !illuminationState;
        setIlluminationLED(illuminationState);
        Serial.printf("Manual LED toggle: %s\n", illuminationState ? "ON" : "OFF");
    }

    JsonDocument doc;
    doc["led_state"] = illuminationState;
    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

// Add missing API endpoints that your UI expects
void handleWhiteBalance() {
    Serial.println("=== WHITE BALANCE CALIBRATION ===");

    // Turn on LED for calibration
    setIlluminationLED(true);

    // Perform proper white balance calibration
    bool success = colorSensor.performWhiteBalanceCalibration(10);

    if (success) {
        server.send(200, "text/plain", "White balance calibration completed successfully");
    } else {
        server.send(500, "text/plain", "White balance calibration failed");
    }

    // Turn LED back to scanning state
    if (!isScanning) {
        setIlluminationLED(false);
    }

    Serial.println("=== WHITE BALANCE COMPLETED ===");
}

void handleDarkCalibration() {
    Serial.println("=== DARK CALIBRATION ===");

    // Turn off LED for dark calibration
    setIlluminationLED(false);

    // Perform dark calibration
    bool success = colorSensor.performDarkCalibration(10);

    if (success) {
        server.send(200, "text/plain", "Dark calibration completed successfully");
    } else {
        server.send(500, "text/plain", "Dark calibration failed");
    }

    Serial.println("=== DARK CALIBRATION COMPLETED ===");
}

void handleOptimizeGain() {
    Serial.println("=== OPTIMIZING GAIN ===");

    uint8_t optimalGain = colorSensor.getOptimalGain();
    colorSensor.setALSGain(optimalGain);

    Serial.printf("Optimal gain set to: %d\n", optimalGain);

    JsonDocument doc;
    doc["optimal_gain"] = optimalGain;
    doc["gain_names"] = JsonArray();
    doc["gain_names"].add("1x");
    doc["gain_names"].add("4x");
    doc["gain_names"].add("16x");
    doc["gain_names"].add("64x");

    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

void handleSetCalibration() {
    // Placeholder for calibration settings
    server.send(200, "text/plain", "Calibration settings saved");
}

void setupWebServer() {
    // Initialize LittleFS
    if (!LittleFS.begin(true)) {
        Serial.println("An Error has occurred while mounting LittleFS");
        return;
    }
    Serial.println("LittleFS mounted successfully");

    // List files in LittleFS for debugging
    Serial.println("Files in LittleFS:");
    File root = LittleFS.open("/");
    File file = root.openNextFile();
    while (file) {
        Serial.print("  ");
        Serial.println(file.name());
        file = root.openNextFile();
    }
    root.close();

    // Load Dulux color database
    if (!loadDuluxDatabase()) {
        // Use fallback colors if dulux.json fails to load
        Serial.println("Using fallback color database");
        COLOR_DATABASE_SIZE = FALLBACK_COLOR_COUNT;
        for (int i = 0; i < FALLBACK_COLOR_COUNT; i++) {
            strncpy(colorDatabase[i].name, fallbackColors[i].name, MAX_COLOR_NAME_LEN - 1);
            colorDatabase[i].name[MAX_COLOR_NAME_LEN - 1] = '\0';
            colorDatabase[i].r = fallbackColors[i].r;
            colorDatabase[i].g = fallbackColors[i].g;
            colorDatabase[i].b = fallbackColors[i].b;
        }
    }

    // Setup web server routes
    server.on("/", handleRoot);
    server.on("/style.css", handleStyleCSS);
    server.on("/fulldata", handleFullData);
    server.on("/start_scan", HTTP_POST, handleStartScan);
    server.on("/stop_scan", HTTP_POST, handleStopScan);
    server.on("/toggle_led", HTTP_POST, handleToggleLED);
    server.on("/white_balance", HTTP_POST, handleWhiteBalance);
    server.on("/dark_calibration", HTTP_POST, handleDarkCalibration);
    server.on("/optimize_gain", HTTP_POST, handleOptimizeGain);
    server.on("/set_calibration", HTTP_POST, handleSetCalibration);

    // Start server
    server.begin();
    Serial.println("Web server started");
    Serial.print("Open http://");
    Serial.print(WiFi.localIP());
    Serial.println(" in your browser");
}

// ------------------------------------------------------------------------------------
// WiFi Functions
// ------------------------------------------------------------------------------------
void initializeWiFi() {
    Serial.println("============================================================");
    Serial.println("WiFi Initialization");
    Serial.println("============================================================");
    Serial.printf("SSID: %s\n", WIFI_SSID);
    Serial.printf("Static IP: %s\n", STATIC_IP.toString().c_str());
    Serial.printf("Gateway: %s\n", GATEWAY.toString().c_str());
    Serial.printf("Subnet: %s\n", SUBNET.toString().c_str());

    // Disconnect any previous connection
    WiFi.disconnect(true);
    delay(1000);

    WiFi.mode(WIFI_STA);

    // Try without static IP first
    Serial.println("Attempting connection with DHCP...");
    WiFi.begin(WIFI_SSID, WIFI_PASSWORD);

    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 30) {
        delay(500);
        Serial.print(".");
        attempts++;

        // Print WiFi status for debugging
        if (attempts % 10 == 0) {
            Serial.println();
            Serial.printf("WiFi Status: %d (attempt %d/30)\n", WiFi.status(), attempts);
        }
    }

    if (WiFi.status() == WL_CONNECTED) {
        wifiConnected = true;
        Serial.println();
        Serial.println("WiFi connected with DHCP!");
        Serial.printf("IP address: %s\n", WiFi.localIP().toString().c_str());
        Serial.printf("Gateway: %s\n", WiFi.gatewayIP().toString().c_str());
        Serial.printf("Subnet: %s\n", WiFi.subnetMask().toString().c_str());
        Serial.printf("DNS: %s\n", WiFi.dnsIP().toString().c_str());

        // Now try to set static IP
        Serial.println("Setting static IP...");
        if (WiFi.config(STATIC_IP, GATEWAY, SUBNET)) {
            Serial.printf("Static IP set successfully: %s\n", WiFi.localIP().toString().c_str());
        } else {
            Serial.println("Failed to set static IP, using DHCP IP");
        }
    } else {
        Serial.println();
        Serial.println("WiFi connection failed - continuing without network");
        Serial.printf("Final WiFi Status: %d\n", WiFi.status());
        wifiConnected = false;
    }
    Serial.println("============================================================");
}

// ------------------------------------------------------------------------------------
// Main Functions
// ------------------------------------------------------------------------------------
void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("============================================================");
    Serial.println("ESP32-S3 Color Matching Device - Working Version");
    Serial.println("============================================================");
    
    // Initialize ProS3 onboard RGB LED
    Serial.printf("Initializing ProS3 RGB LED on GPIO%d...\n", RGB_LED_PIN);
    rgbLED.begin();
    rgbLED.setBrightness(50); // Lower brightness for ProS3 (can be quite bright)
    rgbLED.setPixelColor(0, rgbLED.Color(255, 0, 0)); // Start with red to test
    rgbLED.show();
    delay(500);
    rgbLED.setPixelColor(0, rgbLED.Color(0, 255, 0)); // Green
    rgbLED.show();
    delay(500);
    rgbLED.setPixelColor(0, rgbLED.Color(0, 0, 255)); // Blue
    rgbLED.show();
    delay(500);
    rgbLED.setPixelColor(0, rgbLED.Color(0, 0, 0)); // Off
    rgbLED.show();
    Serial.println("ProS3 RGB LED initialized and tested");

    // Initialize GPIO pins
    pinMode(ILLUMINATION_LED_PIN, OUTPUT);
    pinMode(SENSOR_INTERRUPT_PIN, INPUT_PULLUP);

    digitalWrite(ILLUMINATION_LED_PIN, LOW);
    
    // Initialize I2C
    Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);
    Wire.setClock(400000); // 400kHz
    Serial.println("I2C initialized");
    
    // Initialize color sensor
    sensorInitialized = initializeSensor();
    if (!sensorInitialized) {
        Serial.println("CRITICAL: Sensor initialization failed!");
        Serial.println("Check I2C connections and sensor power");
    }
    
    // Initialize WiFi
    initializeWiFi();

    // Initialize Web Server (only if WiFi connected)
    if (wifiConnected) {
        setupWebServer();
    }

    // Initialize illumination LED to OFF state
    setIlluminationLED(false);
    illuminationState = false;

    Serial.println("============================================================");
    Serial.println("System initialization complete!");
    Serial.println("Illumination LED: OFF (will turn on during scanning/calibration)");
    Serial.println("Color readings: PAUSED (press scan button to start)");
    Serial.println("============================================================");
}

void loop() {
    // Update RGB LED to show system is alive with color cycling
    updateRGBStatusLED();

    // Simple LED control - turn on during scanning, off otherwise
    if (isScanning) {
        setIlluminationLED(true);
    } else {
        setIlluminationLED(false);
    }

    // Read color sensor at specified interval - always read but only print when scanning
    if (sensorInitialized && millis() - lastReading >= READING_INTERVAL_MS) {
        lastReading = millis();

        if (readColorSensor()) {
            // Only print color data when actively scanning
            if (isScanning) {
                Serial.println("--- Color Reading ---");
                Serial.printf("Raw XYZ: X=%d, Y=%d, Z=%d\n", currentColor.x, currentColor.y, currentColor.z);
                Serial.printf("Raw IR: IR1=%d, IR2=%d\n", currentColor.ir1, currentColor.ir2);
                Serial.printf("RGB: R=%d, G=%d, B=%d\n", currentColor.r, currentColor.g, currentColor.b);
                Serial.printf("Hex: %s\n", currentColor.hexColor);
                Serial.printf("Closest Match: %s (Distance: %.1f)\n", currentColor.matchedColor, currentColor.deltaE);
                Serial.printf("WiFi Status: %s\n", wifiConnected ? "Connected" : "Disconnected");
                Serial.println("--------------------");
            }
        }
    }

    // Handle web server requests
    if (wifiConnected) {
        server.handleClient();
    }

    // Memory monitoring (every 30 seconds)
    static unsigned long lastMemoryCheck = 0;
    if (millis() - lastMemoryCheck > 30000) {
        Serial.printf("Memory Status - Free Heap: %d bytes, PSRAM: %d bytes\n",
                     ESP.getFreeHeap(), ESP.getFreePsram());
        lastMemoryCheck = millis();
    }

    // Small delay to prevent overwhelming the system
    delay(10);
}
