/*
 * ESP32-S3 Color Matching Device - Working Version with WiFi
 * 
 * A working version that compiles successfully and provides core functionality
 * with proper WiFi connectivity to "Wifi 6" network.
 * 
 * Features:
 * - TCS3430 color sensor reading with I2C communication
 * - WiFi connectivity using ESP32 built-in WiFi
 * - Simple color matching with built-in color database
 * - LED status indicators
 * - Serial output for debugging and data display
 * - Basic error handling and sensor health checks
 * 
 * Hardware Configuration:
 * - I2C: SDA=8, SCL=9
 * - Status LED: GPIO 5
 * - Illumination LED: GPIO 4  
 * - Sensor Interrupt: GPIO 21
 * 
 * Network Configuration:
 * - SSID: "Wifi 6"
 * - Password: "Scrofani1985"
 * - Static IP: *************
 */

#include <Arduino.h>
#include <Wire.h>
#include <WiFi.h>
#include <DFRobot_TCS3430.h>
#include <ArduinoJson.h>
#include <math.h>
#include <WebServer.h>
#include <LittleFS.h>

// ------------------------------------------------------------------------------------
// Configuration Constants
// ------------------------------------------------------------------------------------
#define I2C_SDA_PIN          8
#define I2C_SCL_PIN          9
#define STATUS_LED_PIN       5
#define ILLUMINATION_LED_PIN 4
#define SENSOR_INTERRUPT_PIN 21
#define READING_INTERVAL_MS  500
#define MAX_COLOR_NAME_LEN   35
#define MAX_DULUX_COLORS     1500

// Network Configuration
const char* WIFI_SSID = "Wifi 6";
const char* WIFI_PASSWORD = "Scrofani1985";
const IPAddress STATIC_IP(192, 168, 0, 201);
const IPAddress GATEWAY(192, 168, 0, 1);
const IPAddress SUBNET(255, 255, 255, 0);

// ------------------------------------------------------------------------------------
// Global Variables
// ------------------------------------------------------------------------------------
DFRobot_TCS3430 colorSensor;
unsigned long lastReading = 0;
unsigned long lastStatusBlink = 0;
bool statusLedState = false;
bool illuminationState = false;
bool sensorInitialized = false;
bool wifiConnected = false;

// Color data structure
struct ColorData {
    uint16_t x, y, z;
    uint16_t ir1, ir2;
    uint8_t r, g, b;
    char hexColor[8];
    char matchedColor[32];
    float deltaE;
};

ColorData currentColor = {0};

// Dulux color database structure
struct ReferenceColor {
    char name[MAX_COLOR_NAME_LEN];
    uint8_t r, g, b;
};

ReferenceColor colorDatabase[MAX_DULUX_COLORS];
int COLOR_DATABASE_SIZE = 0;

// Fallback colors if dulux.json fails to load
const ReferenceColor fallbackColors[] = {
    {"White", 255, 255, 255},
    {"Black", 0, 0, 0},
    {"Red", 255, 0, 0},
    {"Green", 0, 255, 0},
    {"Blue", 0, 0, 255},
    {"Yellow", 255, 255, 0},
    {"Cyan", 0, 255, 255},
    {"Magenta", 255, 0, 255},
    {"Orange", 255, 165, 0},
    {"Purple", 128, 0, 128},
    {"Brown", 165, 42, 42},
    {"Gray", 128, 128, 128},
    {"Pink", 255, 192, 203},
    {"Lime", 0, 255, 0},
    {"Navy", 0, 0, 128}
};

const int FALLBACK_COLOR_COUNT = sizeof(fallbackColors) / sizeof(fallbackColors[0]);

// ------------------------------------------------------------------------------------
// Web Server Variables
// ------------------------------------------------------------------------------------
WebServer server(80);
bool isScanning = false;
unsigned long lastWebUpdate = 0;

// ------------------------------------------------------------------------------------
// Dulux Color Database
// ------------------------------------------------------------------------------------
bool loadDuluxDatabase() {
    if (!LittleFS.exists("/dulux.json")) {
        Serial.println("dulux.json not found, using built-in colors");
        return false;
    }

    File file = LittleFS.open("/dulux.json", "r");
    if (!file) {
        Serial.println("Failed to open dulux.json");
        return false;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        Serial.print("Failed to parse dulux.json: ");
        Serial.println(error.c_str());
        return false;
    }

    // Clear existing database
    COLOR_DATABASE_SIZE = 0;

    JsonArray colors = doc.as<JsonArray>();
    for (JsonVariant colorVar : colors) {
        if (COLOR_DATABASE_SIZE >= MAX_DULUX_COLORS) break;

        JsonObject color = colorVar.as<JsonObject>();
        if (!color["name"].is<const char*>() || !color["r"].is<int>() ||
            !color["g"].is<int>() || !color["b"].is<int>()) continue;

        strncpy(colorDatabase[COLOR_DATABASE_SIZE].name, color["name"], MAX_COLOR_NAME_LEN - 1);
        colorDatabase[COLOR_DATABASE_SIZE].name[MAX_COLOR_NAME_LEN - 1] = '\0';
        colorDatabase[COLOR_DATABASE_SIZE].r = color["r"];
        colorDatabase[COLOR_DATABASE_SIZE].g = color["g"];
        colorDatabase[COLOR_DATABASE_SIZE].b = color["b"];
        COLOR_DATABASE_SIZE++;
    }

    Serial.printf("Loaded %d colors from dulux.json\n", COLOR_DATABASE_SIZE);
    return true;
}

// ------------------------------------------------------------------------------------
// Helper Functions
// ------------------------------------------------------------------------------------
void blinkStatusLED() {
    if (millis() - lastStatusBlink > 1000) {
        statusLedState = !statusLedState;
        digitalWrite(STATUS_LED_PIN, statusLedState ? HIGH : LOW);
        lastStatusBlink = millis();
    }
}

void setIlluminationLED(bool state) {
    illuminationState = state;
    digitalWrite(ILLUMINATION_LED_PIN, state ? HIGH : LOW);
}

// Simple color distance calculation (Euclidean distance in RGB space)
float calculateColorDistance(uint8_t r1, uint8_t g1, uint8_t b1, uint8_t r2, uint8_t g2, uint8_t b2) {
    float dr = r1 - r2;
    float dg = g1 - g2;
    float db = b1 - b2;
    return sqrt(dr*dr + dg*dg + db*db);
}

// Find closest color match in database
void findColorMatch(uint8_t r, uint8_t g, uint8_t b) {
    float minDistance = 999999.0;
    int bestMatch = 0;
    
    for (int i = 0; i < COLOR_DATABASE_SIZE; i++) {
        float distance = calculateColorDistance(r, g, b, 
                                              colorDatabase[i].r, 
                                              colorDatabase[i].g, 
                                              colorDatabase[i].b);
        if (distance < minDistance) {
            minDistance = distance;
            bestMatch = i;
        }
    }
    
    strcpy(currentColor.matchedColor, colorDatabase[bestMatch].name);
    currentColor.deltaE = minDistance;
}

// Convert RGB to hex string
void rgbToHex(uint8_t r, uint8_t g, uint8_t b, char* hexStr) {
    sprintf(hexStr, "#%02X%02X%02X", r, g, b);
}

// ------------------------------------------------------------------------------------
// Sensor Functions
// ------------------------------------------------------------------------------------
bool initializeSensor() {
    Serial.println("Initializing TCS3430 color sensor...");
    
    if (!colorSensor.begin()) {
        Serial.println("ERROR: Failed to initialize TCS3430 sensor!");
        return false;
    }
    
    // Configure sensor settings
    colorSensor.setALSGain(1);           // Set ALS gain
    colorSensor.setIntegrationTime(0x40); // Set integration time
    
    Serial.println("TCS3430 sensor initialized successfully");
    return true;
}

bool readColorSensor() {
    // Read raw sensor data
    currentColor.x = colorSensor.getXData();
    currentColor.y = colorSensor.getYData();
    currentColor.z = colorSensor.getZData();
    currentColor.ir1 = colorSensor.getIR1Data();
    currentColor.ir2 = colorSensor.getIR2Data();
    
    // Simple XYZ to RGB conversion (basic approximation)
    // This is a simplified conversion - can be improved later
    float xNorm = currentColor.x / 65535.0;
    float yNorm = currentColor.y / 65535.0;
    float zNorm = currentColor.z / 65535.0;
    
    // Basic RGB conversion (simplified)
    currentColor.r = constrain(xNorm * 255, 0, 255);
    currentColor.g = constrain(yNorm * 255, 0, 255);
    currentColor.b = constrain(zNorm * 255, 0, 255);
    
    // Generate hex color string
    rgbToHex(currentColor.r, currentColor.g, currentColor.b, currentColor.hexColor);
    
    // Find closest color match
    findColorMatch(currentColor.r, currentColor.g, currentColor.b);
    
    return true;
}

// ------------------------------------------------------------------------------------
// Web Server Functions
// ------------------------------------------------------------------------------------
void handleRoot() {
    File file = LittleFS.open("/index.html", "r");
    if (file) {
        server.streamFile(file, "text/html");
        file.close();
    } else {
        server.send(404, "text/plain", "index.html not found");
    }
}

void handleStyleCSS() {
    File file = LittleFS.open("/style.css", "r");
    if (file) {
        server.streamFile(file, "text/css");
        file.close();
    } else {
        server.send(404, "text/plain", "style.css not found");
    }
}

void handleFullData() {
    JsonDocument doc;
    doc["data_ready"] = true;
    doc["measured_r"] = currentColor.r;
    doc["measured_g"] = currentColor.g;
    doc["measured_b"] = currentColor.b;
    doc["matched_name"] = currentColor.matchedColor;

    // Find matched color RGB values from database
    int matched_r = 0, matched_g = 0, matched_b = 0;
    for (int i = 0; i < COLOR_DATABASE_SIZE; i++) {
        if (strcmp(colorDatabase[i].name, currentColor.matchedColor) == 0) {
            matched_r = colorDatabase[i].r;
            matched_g = colorDatabase[i].g;
            matched_b = colorDatabase[i].b;
            break;
        }
    }

    doc["matched_r"] = matched_r;
    doc["matched_g"] = matched_g;
    doc["matched_b"] = matched_b;
    doc["delta_e"] = currentColor.deltaE;
    doc["confidence"] = currentColor.deltaE < 5 ? "High" : currentColor.deltaE < 15 ? "Medium" : "Low";
    doc["is_scanning"] = isScanning;
    doc["avg_x"] = (float)currentColor.x;
    doc["avg_y"] = (float)currentColor.y;
    doc["avg_z"] = (float)currentColor.z;
    doc["avg_l"] = 0.0; // LAB conversion would go here
    doc["avg_a"] = 0.0;
    doc["avg_b"] = 0.0;
    doc["avg_ir1"] = (float)currentColor.ir1;
    doc["avg_ir2"] = (float)currentColor.ir2;
    doc["led_state"] = illuminationState;

    // Add calibration data for UI
    doc["calib_gain"] = 16; // Default gain
    doc["calib_ir_comp"] = 0.05; // Default IR compensation
    doc["calib_norm"] = 15000; // Default normalization

    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

void handleStartScan() {
    isScanning = true;
    Serial.println("=== SCAN STARTED ===");
    server.send(200, "text/plain", "Scan started");
}

void handleStopScan() {
    isScanning = false;
    Serial.println("=== SCAN STOPPED ===");
    server.send(200, "text/plain", "Scan stopped");
}

void handleToggleLED() {
    // Only allow manual LED toggle when not scanning or white balancing
    if (!isScanning && !isWhiteBalancing) {
        illuminationState = !illuminationState;
        setIlluminationLED(illuminationState);
        Serial.printf("Manual LED toggle: %s\n", illuminationState ? "ON" : "OFF");
    } else {
        Serial.println("LED toggle ignored - scanning or white balance in progress");
    }

    JsonDocument doc;
    doc["led_state"] = illuminationState;
    String json_response;
    serializeJson(doc, json_response);
    server.send(200, "application/json", json_response);
}

// Add missing API endpoints that your UI expects
void handleWhiteBalance() {
    Serial.println("=== WHITE BALANCE CALIBRATION ===");
    Serial.println("Performing white balance calibration...");

    // Simple white balance - just take a reading with LED on
    setIlluminationLED(true);
    delay(500); // Give time for LED to stabilize

    if (readColorSensor()) {
        Serial.printf("White balance reading - XYZ: X=%d, Y=%d, Z=%d\n", currentColor.x, currentColor.y, currentColor.z);
        server.send(200, "text/plain", "White balance calibration completed");
    } else {
        Serial.println("White balance failed - no valid reading");
        server.send(500, "text/plain", "White balance calibration failed");
    }

    // Turn LED back to scanning state
    if (!isScanning) {
        setIlluminationLED(false);
    }

    Serial.println("=== WHITE BALANCE COMPLETED ===");
}

void handleSetCalibration() {
    // Placeholder for calibration settings
    server.send(200, "text/plain", "Calibration settings saved");
}

void setupWebServer() {
    // Initialize LittleFS
    if (!LittleFS.begin(true)) {
        Serial.println("An Error has occurred while mounting LittleFS");
        return;
    }
    Serial.println("LittleFS mounted successfully");

    // List files in LittleFS for debugging
    Serial.println("Files in LittleFS:");
    File root = LittleFS.open("/");
    File file = root.openNextFile();
    while (file) {
        Serial.print("  ");
        Serial.println(file.name());
        file = root.openNextFile();
    }
    root.close();

    // Load Dulux color database
    if (!loadDuluxDatabase()) {
        // Use fallback colors if dulux.json fails to load
        Serial.println("Using fallback color database");
        COLOR_DATABASE_SIZE = FALLBACK_COLOR_COUNT;
        for (int i = 0; i < FALLBACK_COLOR_COUNT; i++) {
            strncpy(colorDatabase[i].name, fallbackColors[i].name, MAX_COLOR_NAME_LEN - 1);
            colorDatabase[i].name[MAX_COLOR_NAME_LEN - 1] = '\0';
            colorDatabase[i].r = fallbackColors[i].r;
            colorDatabase[i].g = fallbackColors[i].g;
            colorDatabase[i].b = fallbackColors[i].b;
        }
    }

    // Setup web server routes
    server.on("/", handleRoot);
    server.on("/style.css", handleStyleCSS);
    server.on("/fulldata", handleFullData);
    server.on("/start_scan", HTTP_POST, handleStartScan);
    server.on("/stop_scan", HTTP_POST, handleStopScan);
    server.on("/toggle_led", HTTP_POST, handleToggleLED);
    server.on("/white_balance", HTTP_POST, handleWhiteBalance);
    server.on("/set_calibration", HTTP_POST, handleSetCalibration);

    // Start server
    server.begin();
    Serial.println("Web server started");
    Serial.print("Open http://");
    Serial.print(WiFi.localIP());
    Serial.println(" in your browser");
}

// ------------------------------------------------------------------------------------
// WiFi Functions
// ------------------------------------------------------------------------------------
void initializeWiFi() {
    Serial.println("Connecting to WiFi...");
    
    WiFi.mode(WIFI_STA);
    WiFi.config(STATIC_IP, GATEWAY, SUBNET);
    WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
    
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
        delay(500);
        Serial.print(".");
        attempts++;
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        wifiConnected = true;
        Serial.println();
        Serial.print("WiFi connected! IP address: ");
        Serial.println(WiFi.localIP());
    } else {
        Serial.println();
        Serial.println("WiFi connection failed - continuing without network");
        wifiConnected = false;
    }
}

// ------------------------------------------------------------------------------------
// Main Functions
// ------------------------------------------------------------------------------------
void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("============================================================");
    Serial.println("ESP32-S3 Color Matching Device - Working Version");
    Serial.println("============================================================");
    
    // Initialize GPIO pins
    pinMode(STATUS_LED_PIN, OUTPUT);
    pinMode(ILLUMINATION_LED_PIN, OUTPUT);
    pinMode(SENSOR_INTERRUPT_PIN, INPUT_PULLUP);
    
    digitalWrite(STATUS_LED_PIN, LOW);
    digitalWrite(ILLUMINATION_LED_PIN, LOW);
    
    // Initialize I2C
    Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);
    Wire.setClock(400000); // 400kHz
    Serial.println("I2C initialized");
    
    // Initialize color sensor
    sensorInitialized = initializeSensor();
    if (!sensorInitialized) {
        Serial.println("CRITICAL: Sensor initialization failed!");
        Serial.println("Check I2C connections and sensor power");
    }
    
    // Initialize WiFi
    initializeWiFi();

    // Initialize Web Server (only if WiFi connected)
    if (wifiConnected) {
        setupWebServer();
    }

    // Initialize illumination LED to OFF state
    setIlluminationLED(false);
    illuminationState = false;

    Serial.println("============================================================");
    Serial.println("System initialization complete!");
    Serial.println("Illumination LED: OFF (will turn on during scanning/calibration)");
    Serial.println("Color readings: PAUSED (press scan button to start)");
    Serial.println("============================================================");
}

void loop() {
    // Blink status LED to show system is alive
    blinkStatusLED();

    // Simple LED control - turn on during scanning, off otherwise
    if (isScanning) {
        setIlluminationLED(true);
    } else {
        setIlluminationLED(false);
    }

    // Read color sensor at specified interval - always read but only print when scanning
    if (sensorInitialized && millis() - lastReading >= READING_INTERVAL_MS) {
        lastReading = millis();

        if (readColorSensor()) {
            // Only print color data when actively scanning
            if (isScanning) {
                Serial.println("--- Color Reading ---");
                Serial.printf("Raw XYZ: X=%d, Y=%d, Z=%d\n", currentColor.x, currentColor.y, currentColor.z);
                Serial.printf("Raw IR: IR1=%d, IR2=%d\n", currentColor.ir1, currentColor.ir2);
                Serial.printf("RGB: R=%d, G=%d, B=%d\n", currentColor.r, currentColor.g, currentColor.b);
                Serial.printf("Hex: %s\n", currentColor.hexColor);
                Serial.printf("Closest Match: %s (Distance: %.1f)\n", currentColor.matchedColor, currentColor.deltaE);
                Serial.printf("WiFi Status: %s\n", wifiConnected ? "Connected" : "Disconnected");
                Serial.println("--------------------");
            }
        }
    }

    // Handle web server requests
    if (wifiConnected) {
        server.handleClient();
    }

    // Small delay to prevent overwhelming the system
    delay(10);
}
