#include "ColorSensor.h"

// Define the static array
const uint8_t ColorSensor::GAIN_LEVELS[4] = {1, 4, 16, 64};

ColorSensor::ColorSensor() : 
    adaptiveSamplingEnabled(true),
    currentGain(1),
    currentIntegrationTime(DEFAULT_INTEGRATION_TIME),
    calibrationFactor(1.0f)
{
    memset(&currentData, 0, sizeof(ColorData));
}

bool ColorSensor::begin() {
    if (!sensor.begin()) {
        return false;
    }
    
    // Initial sensor configuration
    sensor.setIntegrationTime(currentIntegrationTime);
    sensor.setALSGain(currentGain);
    
    // Clear the buffer
    dataBuffer.reset();
    
    return true;
}

bool ColorSensor::update() {
    ColorData newData;
    
    // Read raw sensor data
    newData.raw_X = sensor.getXData();
    newData.raw_Y = sensor.getYData();
    newData.raw_Z = sensor.getZData();
    
    // Check if reading is valid
    if (!isReadingValid(newData)) {
        if (adaptiveSamplingEnabled) {
            optimizeSettings();
            return false; // Skip this reading
        }
    }
    
    // Apply calibration and convert to floating point
    newData.X = newData.raw_X * calibrationFactor;
    newData.Y = newData.raw_Y * calibrationFactor;
    newData.Z = newData.raw_Z * calibrationFactor;
    
    // Calculate chromaticity coordinates
    calculateChromaticity();
    
    // Add to moving average buffer
    dataBuffer.push(newData);
    
    // Update moving average
    updateMovingAverage();
    
    // Adjust settings if needed
    if (adaptiveSamplingEnabled) {
        adjustSettings();
    }
    
    return true;
}

void ColorSensor::calculateChromaticity() {
    float sum = currentData.X + currentData.Y + currentData.Z;
    if (sum > 0) {
        // TCS3430 directly outputs CIE XYZ tristimulus values
        // Calculate CIE 1931 chromaticity coordinates (x,y)
        currentData.x = currentData.X / sum;
        currentData.y = currentData.Y / sum;
        
        // Store Y value (luminance) separately as per CIE 1931
        currentData.Y_luminance = currentData.Y;
        
        // Convert to RGB color space for LED display
        // Using standard sRGB transformation matrix
        float r = ( 3.2406f * currentData.X - 1.5372f * currentData.Y - 0.4986f * currentData.Z) / 100.0f;
        float g = (-0.9689f * currentData.X + 1.8758f * currentData.Y + 0.0415f * currentData.Z) / 100.0f;
        float b = ( 0.0557f * currentData.X - 0.2040f * currentData.Y + 1.0570f * currentData.Z) / 100.0f;
        
        // Clip values to valid range
        currentData.r = uint8_t(constrain(r * 255.0f, 0.0f, 255.0f));
        currentData.g = uint8_t(constrain(g * 255.0f, 0.0f, 255.0f));
        currentData.b = uint8_t(constrain(b * 255.0f, 0.0f, 255.0f));
        
        // Convert RGB to hex
        sprintf(currentData.hex, "#%02X%02X%02X", currentData.r, currentData.g, currentData.b);
    } else {
        currentData.x = 0;
        currentData.y = 0;
        currentData.Y_luminance = 0;
        currentData.r = 0;
        currentData.g = 0;
        currentData.b = 0;
        strcpy(currentData.hex, "#000000");
    }
}

bool ColorSensor::isReadingValid(const ColorData& data) {
    // Check for sensor saturation
    if (data.raw_X >= SATURATION_THRESHOLD || 
        data.raw_Y >= SATURATION_THRESHOLD || 
        data.raw_Z >= SATURATION_THRESHOLD) {
        return false;
    }
    
    // Check for minimum signal strength
    if (data.raw_Y < LOW_SIGNAL_THRESHOLD) {
        return false;
    }
    
    return true;
}

void ColorSensor::optimizeSettings() {
    ColorData sample;
    sample.raw_X = sensor.getXData();
    sample.raw_Y = sensor.getYData();
    sample.raw_Z = sensor.getZData();
    
    // Check for saturation
    if (sample.raw_X >= SATURATION_THRESHOLD || 
        sample.raw_Y >= SATURATION_THRESHOLD || 
        sample.raw_Z >= SATURATION_THRESHOLD) {
        // Decrease gain or integration time
        if (currentGain > GAIN_LEVELS[0]) {
            // Find next lower gain
            for (int i = 3; i >= 0; i--) {
                if (GAIN_LEVELS[i] < currentGain) {
                    setGain(GAIN_LEVELS[i]);
                    break;
                }
            }
        } else if (currentIntegrationTime > MIN_INTEGRATION_TIME) {
            setIntegrationTime(currentIntegrationTime / 2);
        }
    }
    // Check for low signal
    else if (sample.raw_Y < LOW_SIGNAL_THRESHOLD) {
        // Increase gain or integration time
        if (currentGain < GAIN_LEVELS[3]) {
            // Find next higher gain
            for (int i = 0; i < 4; i++) {
                if (GAIN_LEVELS[i] > currentGain) {
                    setGain(GAIN_LEVELS[i]);
                    break;
                }
            }
        } else if (currentIntegrationTime < MAX_INTEGRATION_TIME) {
            setIntegrationTime(currentIntegrationTime * 2);
        }
    }
}

void ColorSensor::updateMovingAverage() {
    if (dataBuffer.empty()) {
        return;
    }
    
    float sumX = 0, sumY = 0, sumZ = 0;
    size_t count = dataBuffer.size();
    
    for (size_t i = 0; i < count; i++) {
        const ColorData& data = dataBuffer[i];
        sumX += data.X;
        sumY += data.Y;
        sumZ += data.Z;
    }
    
    currentData.X = sumX / count;
    currentData.Y = sumY / count;
    currentData.Z = sumZ / count;
    
    calculateChromaticity();
}

void ColorSensor::adjustSettings() {
    // Implement dynamic adjustment based on current readings
    if (!dataBuffer.isFull()) {
        return; // Wait for buffer to fill
    }
    
    // Calculate variance to detect unstable readings
    float meanY = 0;
    float varianceY = 0;
    
    // First pass: calculate mean
    for (size_t i = 0; i < dataBuffer.size(); i++) {
        meanY += dataBuffer[i].Y;
    }
    meanY /= dataBuffer.size();
    
    // Second pass: calculate variance
    for (size_t i = 0; i < dataBuffer.size(); i++) {
        float diff = dataBuffer[i].Y - meanY;
        varianceY += diff * diff;
    }
    varianceY /= dataBuffer.size();
    
    // If readings are unstable, increase integration time
    if (varianceY > (meanY * 0.1)) { // More than 10% variation
        if (currentIntegrationTime < MAX_INTEGRATION_TIME) {
            uint8_t newTime = currentIntegrationTime * 2;
            setIntegrationTime(newTime > MAX_INTEGRATION_TIME ? MAX_INTEGRATION_TIME : newTime);
        }
    }
}

void ColorSensor::setGain(uint8_t gain) {
    currentGain = gain;
    sensor.setALSGain(gain);
    dataBuffer.reset(); // Clear buffer after changing settings
}

void ColorSensor::setIntegrationTime(uint8_t time) {
    currentIntegrationTime = time;
    sensor.setIntegrationTime(time);
    dataBuffer.reset(); // Clear buffer after changing settings
}

void ColorSensor::calibrate() {
    // Perform white calibration
    // Assuming a white reference target with known Y value of 100
    const float TARGET_Y = 100.0f;
    
    ColorData cal;
    float sumY = 0;
    const int SAMPLES = 10;
    
    // Take multiple samples
    for (int i = 0; i < SAMPLES; i++) {
        cal.raw_Y = sensor.getYData();
        sumY += cal.raw_Y;
        delay(50); // Short delay between samples
    }
    
    float avgY = sumY / SAMPLES;
    if (avgY > 0) {
        calibrationFactor = TARGET_Y / avgY;
    }
    
    dataBuffer.reset(); // Clear buffer after calibration
}
