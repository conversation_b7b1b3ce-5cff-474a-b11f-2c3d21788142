#ifndef HARDWARE_TEST_H
#define HARDWARE_TEST_H

/**
 * Hardware Test Module for ESP32-S3 Color Matching Device
 * 
 * This module provides comprehensive hardware testing functionality
 * for all components of the ESP32-S3-Touch-LCD-1.69 board.
 * 
 * To use this as the main program:
 * 1. Rename main_lvgl.cpp to something else (e.g., main_lvgl_backup.cpp)
 * 2. Rename HardwareTest.cpp to main.cpp
 * 3. Change hardwareTestSetup() to setup() and hardwareTestLoop() to loop()
 */

/**
 * Initialize all hardware components for testing
 * Call this function to start the hardware test sequence
 */
void hardwareTestSetup();

/**
 * Main hardware test loop
 * Cycles through all hardware tests in sequence
 */
void hardwareTestLoop();

/**
 * Individual test functions
 */
void testRGBLED();
void testTFT();
void testButtons();
void testColorSensor();
void testBLE();
void testStorage();
void displayResults();

#endif // HARDWARE_TEST_H
